import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import hashlib
import numpy as np
from PIL import Image, ImageTk
import os
import threading
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import queue
import time
from collections import defaultdict
import json

class ImageHasher:
    """Advanced image hashing for perfect similarity detection"""
    
    def __init__(self):
        self.hash_size = 16  # Higher resolution for better accuracy
    
    def dhash(self, image, hash_size=None):
        """Difference hash - very accurate for similar images"""
        if hash_size is None:
            hash_size = self.hash_size
            
        # Resize and convert to grayscale
        image = image.convert('L').resize((hash_size + 1, hash_size), Image.LANCZOS)
        pixels = np.array(image)
        
        # Calculate horizontal gradients
        diff = pixels[:, 1:] > pixels[:, :-1]
        return sum([2**i for (i, v) in enumerate(diff.flatten()) if v])
    
    def phash(self, image, hash_size=None):
        """Perceptual hash - good for rotated/scaled versions"""
        if hash_size is None:
            hash_size = self.hash_size
            
        # Convert to grayscale and resize
        image = image.convert('L').resize((hash_size, hash_size), Image.LANCZOS)
        pixels = np.array(image, dtype=np.float32)
        
        # Apply DCT (simplified version)
        dct = np.zeros((hash_size, hash_size))
        for u in range(hash_size):
            for v in range(hash_size):
                sum_val = 0
                for x in range(hash_size):
                    for y in range(hash_size):
                        sum_val += pixels[x, y] * np.cos((2*x+1)*u*np.pi/(2*hash_size)) * np.cos((2*y+1)*v*np.pi/(2*hash_size))
                dct[u, v] = sum_val
        
        # Use top-left 8x8 and get median
        dct_low = dct[:8, :8]
        med = np.median(dct_low)
        diff = dct_low > med
        return sum([2**i for (i, v) in enumerate(diff.flatten()) if v])
    
    def ahash(self, image, hash_size=None):
        """Average hash - basic but reliable"""
        if hash_size is None:
            hash_size = self.hash_size
            
        image = image.convert('L').resize((hash_size, hash_size), Image.LANCZOS)
        pixels = np.array(image)
        avg = pixels.mean()
        diff = pixels > avg
        return sum([2**i for (i, v) in enumerate(diff.flatten()) if v])
    
    def composite_hash(self, image):
        """Combine multiple hashes for maximum accuracy"""
        return {
            'dhash': self.dhash(image),
            'phash': self.phash(image),
            'ahash': self.ahash(image)
        }
    
    def hamming_distance(self, hash1, hash2):
        """Calculate hamming distance between hashes"""
        return bin(hash1 ^ hash2).count('1')
    
    def similarity_score(self, hashes1, hashes2, weights={'dhash': 0.5, 'phash': 0.3, 'ahash': 0.2}):
        """Calculate weighted similarity score (0-1, 1 being identical)"""
        total_score = 0
        for hash_type, weight in weights.items():
            distance = self.hamming_distance(hashes1[hash_type], hashes2[hash_type])
            max_distance = 64  # For 8x8 hash
            similarity = 1 - (distance / max_distance)
            total_score += similarity * weight
        return total_score

class ImageCombinerTool:
    def __init__(self, root):
        self.root = root
        self.root.title("Image Similarity Combiner - Perfect Accuracy")
        self.root.geometry("900x700")
        
        self.hasher = ImageHasher()
        self.image_hashes = {}
        self.similarity_groups = []
        self.processed_images = 0
        self.total_images = 0
        self.cancel_processing = False
        self.similarity_threshold = 0.85  # High threshold for perfect accuracy
        
        self.setup_ui()
        
    def setup_ui(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = ttk.Label(main_frame, text="Image Similarity Combiner", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 10))
        
        # Settings frame
        settings_frame = ttk.LabelFrame(main_frame, text="Settings", padding="5")
        settings_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Similarity threshold
        ttk.Label(settings_frame, text="Similarity Threshold:").grid(row=0, column=0, padx=(0, 10))
        self.threshold_var = tk.DoubleVar(value=self.similarity_threshold)
        threshold_scale = ttk.Scale(settings_frame, from_=0.5, to=0.99, 
                                   variable=self.threshold_var, orient=tk.HORIZONTAL, length=200)
        threshold_scale.grid(row=0, column=1, padx=(0, 10))
        self.threshold_label = ttk.Label(settings_frame, text=f"{self.similarity_threshold:.2f}")
        self.threshold_label.grid(row=0, column=2)
        threshold_scale.configure(command=self.update_threshold_label)
        
        # Worker threads
        ttk.Label(settings_frame, text="Worker Threads:").grid(row=1, column=0, padx=(0, 10), pady=(5, 0))
        self.threads_var = tk.IntVar(value=4)
        threads_spin = ttk.Spinbox(settings_frame, from_=1, to=16, textvariable=self.threads_var, width=5)
        threads_spin.grid(row=1, column=1, sticky=tk.W, pady=(5, 0))
        
        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=2, column=0, columnspan=3, pady=(0, 10))
        
        self.select_btn = ttk.Button(buttons_frame, text="Select Image Folder", command=self.select_folder)
        self.select_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.process_btn = ttk.Button(buttons_frame, text="Process Images", command=self.process_images, state=tk.DISABLED)
        self.process_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.cancel_btn = ttk.Button(buttons_frame, text="Cancel", command=self.cancel_process, state=tk.DISABLED)
        self.cancel_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.save_btn = ttk.Button(buttons_frame, text="Save Results", command=self.save_results, state=tk.DISABLED)
        self.save_btn.pack(side=tk.LEFT)
        
        # Progress frame
        progress_frame = ttk.LabelFrame(main_frame, text="Progress", padding="5")
        progress_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), padx=(0, 10))
        
        self.progress_label = ttk.Label(progress_frame, text="Ready")
        self.progress_label.grid(row=0, column=2)
        
        # Results frame
        results_frame = ttk.LabelFrame(main_frame, text="Similar Image Groups", padding="5")
        results_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # Treeview for results
        self.tree = ttk.Treeview(results_frame, columns=("Count", "Similarity", "Files"), show="headings", height=15)
        self.tree.heading("Count", text="Images")
        self.tree.heading("Similarity", text="Avg Similarity")
        self.tree.heading("Files", text="Sample Files")
        
        self.tree.column("Count", width=80)
        self.tree.column("Similarity", width=100)
        self.tree.column("Files", width=400)
        
        scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # Status bar
        self.status_var = tk.StringVar(value="Select a folder to begin")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # Configure grid weights
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)
        main_frame.grid_rowconfigure(4, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)
        results_frame.grid_rowconfigure(0, weight=1)
        results_frame.grid_columnconfigure(0, weight=1)
    
    def update_threshold_label(self, value):
        self.similarity_threshold = float(value)
        self.threshold_label.config(text=f"{self.similarity_threshold:.2f}")
    
    def select_folder(self):
        folder_path = filedialog.askdirectory(title="Select Image Folder")
        if folder_path:
            self.image_folder = folder_path
            # Count images
            image_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.gif')
            image_files = []
            for root, dirs, files in os.walk(folder_path):
                for file in files:
                    if file.lower().endswith(image_extensions):
                        image_files.append(os.path.join(root, file))
            
            self.image_files = image_files
            self.total_images = len(image_files)
            
            if self.total_images > 0:
                self.status_var.set(f"Found {self.total_images} images in {folder_path}")
                self.process_btn.config(state=tk.NORMAL)
            else:
                self.status_var.set("No images found in selected folder")
                messagebox.showwarning("No Images", "No supported image files found in the selected folder.")
    
    def hash_image_worker(self, file_path):
        """Worker function to hash a single image"""
        try:
            with Image.open(file_path) as img:
                # Convert RGBA to RGB if necessary
                if img.mode in ('RGBA', 'LA'):
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                    img = background
                elif img.mode != 'RGB':
                    img = img.convert('RGB')
                
                hashes = self.hasher.composite_hash(img)
                return file_path, hashes
        except Exception as e:
            print(f"Error processing {file_path}: {e}")
            return None, None
    
    def process_images(self):
        """Process all images with multi-threading"""
        self.cancel_processing = False
        self.processed_images = 0
        self.image_hashes = {}
        self.similarity_groups = []
        
        # Clear previous results
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        self.process_btn.config(state=tk.DISABLED)
        self.cancel_btn.config(state=tk.NORMAL)
        self.save_btn.config(state=tk.DISABLED)
        
        self.similarity_threshold = self.threshold_var.get()
        
        def processing_thread():
            try:
                # Phase 1: Hash all images
                self.status_var.set("Phase 1/2: Hashing images...")
                with ThreadPoolExecutor(max_workers=self.threads_var.get()) as executor:
                    future_to_file = {executor.submit(self.hash_image_worker, file_path): file_path 
                                    for file_path in self.image_files}
                    
                    for future in as_completed(future_to_file):
                        if self.cancel_processing:
                            executor.shutdown(wait=False)
                            return
                        
                        file_path, hashes = future.result()
                        if hashes is not None:
                            self.image_hashes[file_path] = hashes
                        
                        self.processed_images += 1
                        progress = (self.processed_images / self.total_images) * 50  # 50% for hashing
                        self.progress_var.set(progress)
                        self.progress_label.config(text=f"Hashing: {self.processed_images}/{self.total_images}")
                        self.root.update_idletasks()
                
                if self.cancel_processing:
                    return
                
                # Phase 2: Find similar groups
                self.status_var.set("Phase 2/2: Finding similar images...")
                self.find_similar_groups()
                
                # Display results
                self.display_results()
                
                self.status_var.set(f"Found {len(self.similarity_groups)} groups of similar images")
                self.save_btn.config(state=tk.NORMAL)
                
            except Exception as e:
                messagebox.showerror("Error", f"An error occurred: {str(e)}")
            finally:
                self.process_btn.config(state=tk.NORMAL)
                self.cancel_btn.config(state=tk.DISABLED)
                self.progress_var.set(0)
                self.progress_label.config(text="Ready")
        
        threading.Thread(target=processing_thread, daemon=True).start()
    
    def find_similar_groups(self):
        """Find groups of similar images"""
        files = list(self.image_hashes.keys())
        used_files = set()
        total_comparisons = len(files) * (len(files) - 1) // 2
        current_comparison = 0
        
        for i, file1 in enumerate(files):
            if self.cancel_processing:
                return
            
            if file1 in used_files:
                continue
            
            group = [file1]
            similarities = []
            
            for j, file2 in enumerate(files[i+1:], i+1):
                current_comparison += 1
                
                if file2 in used_files:
                    continue
                
                similarity = self.hasher.similarity_score(
                    self.image_hashes[file1], 
                    self.image_hashes[file2]
                )
                
                if similarity >= self.similarity_threshold:
                    group.append(file2)
                    similarities.append(similarity)
                
                # Update progress
                if current_comparison % 100 == 0:
                    progress = 50 + (current_comparison / total_comparisons) * 50
                    self.progress_var.set(progress)
                    self.progress_label.config(text=f"Comparing: {current_comparison}/{total_comparisons}")
                    self.root.update_idletasks()
            
            # Only keep groups with multiple images
            if len(group) > 1:
                avg_similarity = sum(similarities) / len(similarities) if similarities else 1.0
                self.similarity_groups.append({
                    'files': group,
                    'avg_similarity': avg_similarity,
                    'count': len(group)
                })
                used_files.update(group)
        
        # Sort groups by count (descending)
        self.similarity_groups.sort(key=lambda x: x['count'], reverse=True)
    
    def display_results(self):
        """Display similarity groups in the treeview"""
        for group in self.similarity_groups:
            sample_files = []
            for file_path in group['files'][:3]:  # Show first 3 files
                sample_files.append(os.path.basename(file_path))
            
            if len(group['files']) > 3:
                sample_files.append(f"... and {len(group['files']) - 3} more")
            
            self.tree.insert('', tk.END, values=(
                group['count'],
                f"{group['avg_similarity']:.3f}",
                ", ".join(sample_files)
            ))
    
    def cancel_process(self):
        """Cancel the current processing"""
        self.cancel_processing = True
        self.status_var.set("Cancelling...")
    
    def save_results(self):
        """Save results to a JSON file"""
        if not self.similarity_groups:
            messagebox.showwarning("No Results", "No similarity groups to save.")
            return
        
        file_path = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            title="Save Results"
        )
        
        if file_path:
            try:
                results = {
                    'settings': {
                        'similarity_threshold': self.similarity_threshold,
                        'total_images': self.total_images,
                        'groups_found': len(self.similarity_groups)
                    },
                    'groups': []
                }
                
                for group in self.similarity_groups:
                    results['groups'].append({
                        'count': group['count'],
                        'avg_similarity': group['avg_similarity'],
                        'files': group['files']
                    })
                
                with open(file_path, 'w') as f:
                    json.dump(results, f, indent=2)
                
                messagebox.showinfo("Success", f"Results saved to {file_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save results: {str(e)}")

def main():
    root = tk.Tk()
    app = ImageCombinerTool(root)
    root.mainloop()

if __name__ == "__main__":
    main()